# Copyright (C) 2024 The Tongsuo Project Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Tongsuo library integration for trusty-tee using pre-built libraries
LOCAL_DIR := $(GET_LOCAL_DIR)
MODULE := $(LOCAL_DIR)

# Set component path for compatibility
COMP_PATH := $(LOCAL_DIR)

# Export Tongsuo headers with symbol prefix support
MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

# Symbol prefix configuration (already handled by Configure)
MODULE_CFLAGS += -DOPENSSL_SYMBOL_PREFIX=TONGSUO_

# Don't use stub source file - we'll override the library creation process
# MODULE_SRCS += $(LOCAL_DIR)/tongsuo_stub.c

# Include the build system but override the library creation
include make/compability.mk

# Re-set component path after variables are reset by compatibility layer
COMP_PATH := $(LOCAL_DIR)

# Define our library target
LIB_BIN := $(COMP_OUTDIR)/lib$(LIB_NAME).a

# Override the default library creation with our pre-built library
$(LIB_BIN): $(LOCAL_DIR)/libTongsuo.a
	@echo "Installing pre-built Tongsuo library: $@"
	@mkdir -p $(dir $@)
	@cp $< $@
	@echo "Library size after copy: $$(stat -c%s $@) bytes"

# Add to build targets
TOTAL_USER_TARGETS += $(LIB_BIN)

# Reset variables as done in rctee_lib.mk
COMP_OBJS :=
COMP_OUTDIR :=
LIB_NAME :=
LIB_BIN :=
LIB_SRC_DEPS :=
LIB_BIN_DEPS :=
LIB_LINK_PATH :=
LIB_LINK_DEPS :=


