# Copyright (C) 2015 The Android Open Source Project
# Copyright NXP 2020
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

TARGET := imx8mp

WITH_SNVS_DRIVER := true

#Enlarge imx8mp storage size to 2048 blocks.
STORAGE_RPMB_BLOCK_COUNT = 2048
MEMBASE           := 0x56000000

# caam support
WITH_CAAM_SUPPORT := true

include project/imx8-inc.mk

# add busy-test ktipc-test 
MODULES += \
    kernel/rctee/app/busytest \
    kernel/rctee/lib/ktipc/test/srv \
	kernel/rctee/lib/ktipc/test/main \
    kernel/rctee/services/generic_ta_service \
# add tipc-test manifest-test
# disable trusty/hardware/nxp/app/hwsecure
# disable trusty/user/base/app/hwsecure_client
# disable trusty/hardware/nxp/app/secure_fb_impl
BUNDLED_TA_TASKS += \
    user/app/sample/memref-test/receiver \
    user/app/sample/manifest-test \
    user/app/sample/tongsuo-mini-demo \
    user/base/app/tongsuo-test \
#    user/base/lib/tipc/test/main \
#    user/base/lib/tipc/test/srv \

DYNAMIC_TA_TASKS += \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1 \

ifeq (true,$(call TOBOOL,$(ENABLE_RCTEE_TEST)))
BUNDLED_TA_TASKS += \
    user/base/app/rctee-test/static_ta_test/user_api_test_static \
    user/base/app/rctee-test/static_ta_test/test_rcipc_read_write_iter \
    user/base/app/rctee-test/static_ta_test/test_rcipc_large_file \
    user/base/app/rctee-test/static_ta_test/test_rcipc_ta2ta \
    user/base/app/rctee-test/static_ta_test/test_rcipc_echo \

DYNAMIC_TA_TASKS += \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_1 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_3 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_4 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_5 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_7 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_8 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_9 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_10 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_11 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_12 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_13 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_14 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_15 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_16 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_17 \
    user/base/app/rctee-test/dynamic_ta_test/dynamic_ta_test_18 \
    user/base/app/rctee-test/dynamic_ta_test/user_api_test_dynamic \

endif

# Change this to specify the LCDIF device on imx8mp
GLOBAL_DEFINES += IMX8MP_LCDIF_INDEX=1

WITH_LCDIF_SUPPORT := true

TRUSTY_PROVISIONING_METHOD := OEMCrypto_Keybox

CONFIRMATIONUI_DEVICE_PARAMS := hardware/nxp/user/lib/tui_device_params

ifeq (true,$(call TOBOOL,$(BUILD_WIDEVINE)))
DYNAMIC_TA_TASKS += \
    private/oemcrypto/oemcrypto/opk/ports/trusty/ta/reference

NEED_PARSE_HEADER := true
endif

WITH_VPU_DECODER_DRIVER := true
WITH_VPU_ENCODER_DRIVER := true

WITH_FFA_SUPPORT := true
ifeq (true,$(call TOBOOL,$(WITH_FFA_SUPPORT)))
GLOBAL_DEFINES += WITH_FFA_SUPPORT=1
endif

CFG_DUTA := true
ifeq (true,$(call TOBOOL,$(CFG_DUTA)))
GLOBAL_DEFINES += CFG_DUTA=1
endif

CFG_CANCELL := true
ifeq (true,$(call TOBOOL,$(CFG_CANCELL)))
GLOBAL_DEFINES += CFG_CANCELL=1
endif